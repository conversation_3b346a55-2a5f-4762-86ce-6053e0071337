/* Footer Base Styles */

#footer {
  background-color: #f8fafc;
  border-top: 1px solid #e2e8f0;
  margin-top: 4rem;
  position: relative;
  overflow: hidden;
  height: 1500px;
  display: flex;
  flex-direction: column;
}

.footer-wrapper {
  position: relative;
  padding: 5rem 0 0 0;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.footer-background-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 13rem;
  font-weight: 900;
  color: rgba(0, 0, 0, 0.03);
  z-index: 1;
  pointer-events: none;
  user-select: none;
  white-space: nowrap;
  word-spacing: 0.5em;
  text-align: center;
}

.container {
  max-width: none;
  width: 90%;
  margin: 0 auto;
  padding: 0 3rem;
  position: relative;
  z-index: 2;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.footer-spacer {
  flex-grow: 1;
}

/* Responsive base adjustments */
@media (max-width: 1024px) {
  .footer-background-text {
    font-size: 9rem;
    word-spacing: 0.9em;
  }

  .container {
    padding: 0 2rem;
  }
}

@media (max-width: 768px) {
  .footer-wrapper {
    padding: 4rem 0 0 0;
  }

  .container {
    padding: 0 1.5rem;
  }

  .footer-background-text {
    font-size: 7rem;
    word-spacing: 0.3em;
  }
}

@media (max-width: 480px) {
  .footer-background-text {
    font-size: 5rem;
    word-spacing: 0.2em;
  }
}
