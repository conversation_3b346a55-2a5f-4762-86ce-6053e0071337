/* Footer Content Styles */

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 5rem;
  margin-bottom: auto;
  flex-grow: 1;
  align-content: start;
  padding: 2rem 0;
  width: 100%;
  max-width: 1500px;
  text-align: left;
  justify-self: center;
}

.footer-section {
  display: flex;
  flex-direction: column;
}

.footer-brand {
  max-width: 400px;
}

.footer-brand-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 2rem;
}

.footer-description {
  color: #64748b;
  font-size: 1.25rem;
  line-height: 1.7;
  margin-bottom: 0;
}

.footer-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 2.5rem;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 1.25rem;
}

.footer-links a {
  color: #64748b;
  text-decoration: none;
  font-size: 1.125rem;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: #2563eb;
}

/* Responsive content adjustments */
@media (max-width: 1024px) {
  .footer-content {
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
  }

  .footer-brand {
    grid-column: 1 / -1;
    max-width: none;
    margin-bottom: 2rem;
  }
}

@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr;
    gap: 3rem;
  }
}

@media (max-width: 480px) {
  .footer-brand-title {
    font-size: 2rem;
  }

  .footer-description {
    font-size: 1rem;
  }

  .footer-title {
    font-size: 1.25rem;
  }

  .footer-links a {
    font-size: 1rem;
  }
}
